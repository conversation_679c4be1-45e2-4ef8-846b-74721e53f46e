{"/Users/<USER>/Library/Application Support/Windsurf/WebStorage/5/CacheStorage/54794c7d-284f-47dc-a708-14503389b435/a4c6aac8b18c180d_0": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "WebStorage/5/CacheStorage/54794c7d-284f-47dc-a708-14503389b435/a4c6aac8b18c180d_0"}, "/Users/<USER>/Library/Application Support/Windsurf/WebStorage/5/CacheStorage/54794c7d-284f-47dc-a708-14503389b435/todelete_a4c6aac8b18c180d_0_1": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "WebStorage/5/CacheStorage/54794c7d-284f-47dc-a708-14503389b435/todelete_a4c6aac8b18c180d_0_1"}, "/Users/<USER>/Library/Application Support/Windsurf/TransportSecurity": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "TransportSecurity"}, "/Users/<USER>/Library/Application Support/Windsurf/User/globalStorage/augment.vscode-augment/augment-global-state/terminalSettings.json": {"rootPath": "/Users/<USER>/Library/Application Support/Windsurf", "relPath": "User/globalStorage/augment.vscode-augment/augment-global-state/terminalSettings.json"}}